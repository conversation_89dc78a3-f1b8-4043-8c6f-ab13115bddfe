#!/usr/bin/env python3
"""
Test script to check if all imports work correctly
"""

print("Testing imports...")

try:
    print("Testing image_enhancer...")
    from image_enhancer import ImageEnhancer
    print("✓ image_enhancer imported successfully")
except Exception as e:
    print(f"✗ image_enhancer failed: {e}")

try:
    print("Testing background_remover...")
    from background_remover import BackgroundRemover
    print("✓ background_remover imported successfully")
except Exception as e:
    print(f"✗ background_remover failed: {e}")

try:
    print("Testing tkinter...")
    import tkinter as tk
    print("✓ tkinter imported successfully")
except Exception as e:
    print(f"✗ tkinter failed: {e}")

print("All import tests completed!")
