# محسن الصور ومزيل الخلفية - Image Enhancement & Background Removal

تطبيق متقدم لتحسين جودة الصور وإزالة الخلفية باستخدام الذكاء الاصطناعي.

## المميزات

### 🚀 تحسين الصور (Image Enhancement)
- استخدام تقنية Real-ESRGAN المتقدمة
- دعم كرت الشاشة (GPU) للمعالجة السريعة
- تكبير الصور بمعاملات 2x أو 4x
- تحسين الجودة والوضوح بشكل كبير
- معالجة مجلدات كاملة من الصور

### 🎨 إزالة الخلفية (Background Removal)
- استخدام مكتبة rembg المتطورة
- نماذج متعددة للإزالة (u2net, u2netp, u2net_human_seg, silueta)
- حفظ بخلفية شفافة (PNG) أو خلفية بيضاء (JPG)
- معالجة احترافية للصور

### 💻 واجهة سهلة الاستخدام
- واجهة رسومية بسيطة ومفهومة
- تبويبات منفصلة لكل وظيفة
- شريط تقدم لمتابعة العملية
- سجل مفصل للعمليات

## متطلبات النظام

### الأجهزة
- **للمعالجة بكرت الشاشة**: NVIDIA GPU مع CUDA
- **للمعالجة بالمعالج**: أي معالج حديث
- **الذاكرة**: 8GB RAM أو أكثر (مستحسن)
- **التخزين**: 5GB مساحة فارغة

### البرمجيات
- Python 3.8 أو أحدث
- pip (مدير الحزم)

## التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر
```bash
# 1. تشغيل التطبيق (سيقوم بتثبيت المتطلبات تلقائياً)
python run_app.py
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. تشغيل التطبيق
python main_gui.py
```

## كيفية الاستخدام

### تحسين الصور
1. افتح تبويب "تحسين الصور"
2. اضغط "اختر المجلد" واختر مجلد الصور
3. اختر معامل التكبير (2x أو 4x)
4. فعّل "استخدام كرت الشاشة" للمعالجة السريعة
5. اضغط "بدء تحسين الصور"
6. انتظر حتى انتهاء العملية
7. اضغط "فتح مجلد النتائج" لرؤية الصور المحسنة

### إزالة الخلفية
1. افتح تبويب "إزالة الخلفية"
2. اضغط "اختر المجلد" واختر مجلد الصور
3. اختر نموذج الإزالة المناسب:
   - **u2net**: عام ومتوازن
   - **u2netp**: أسرع وأقل دقة
   - **u2net_human_seg**: مخصص للأشخاص
   - **silueta**: للكائنات البسيطة
4. اختر تنسيق الحفظ (شفاف أو بخلفية بيضاء)
5. اضغط "بدء إزالة الخلفية"
6. انتظر حتى انتهاء العملية
7. اضغط "فتح مجلد النتائج" لرؤية النتائج

## هيكل المشروع

```
project/
├── main_gui.py              # الواجهة الرسومية الرئيسية
├── image_enhancer.py        # وحدة تحسين الصور
├── background_remover.py    # وحدة إزالة الخلفية
├── run_app.py              # ملف التشغيل
├── requirements.txt        # المتطلبات
├── README.md              # هذا الملف
├── input/                 # مجلد الصور الأصلية (اختياري)
├── output_enhanced/       # مجلد الصور المحسنة
└── output_background_removed/  # مجلد الصور بدون خلفية
```

## الصيغ المدعومة

### صيغ الإدخال
- JPG/JPEG
- PNG
- BMP
- TIFF
- WebP

### صيغ الإخراج
- **تحسين الصور**: PNG (جودة عالية)
- **إزالة الخلفية**: PNG (شفاف) أو JPG (خلفية بيضاء)

## نصائح للاستخدام الأمثل

### لتحسين الصور
- استخدم كرت الشاشة للمعالجة السريعة
- ابدأ بمعامل 2x للصور الكبيرة
- استخدم معامل 4x للصور الصغيرة أو المتوسطة

### لإزالة الخلفية
- استخدم u2net للاستخدام العام
- استخدم u2net_human_seg للصور الشخصية
- احفظ بصيغة PNG للحصول على خلفية شفافة

## استكشاف الأخطاء

### مشاكل شائعة
1. **خطأ في تهيئة GPU**: تأكد من تثبيت CUDA وتحديث تعريفات كرت الشاشة
2. **نفاد الذاكرة**: قلل من حجم الصور أو استخدم المعالج بدلاً من كرت الشاشة
3. **بطء في المعالجة**: تأكد من تفعيل استخدام كرت الشاشة

### الحصول على المساعدة
- تحقق من تبويب "السجل" لرؤية تفاصيل الأخطاء
- تأكد من تثبيت جميع المتطلبات بشكل صحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الشكر والتقدير

- **Real-ESRGAN**: لتقنية تحسين الصور المتقدمة
- **rembg**: لتقنية إزالة الخلفية الاحترافية
- **PyTorch**: لإطار العمل الأساسي للذكاء الاصطناعي
