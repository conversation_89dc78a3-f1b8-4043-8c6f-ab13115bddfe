#!/usr/bin/env python3
"""
Installation script for required packages
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a single package"""
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False

def main():
    """Main installation function"""
    print("=== تثبيت متطلبات التطبيق ===")
    print("Installing Application Requirements")
    print("=" * 40)
    
    # Essential packages first
    essential_packages = [
        "torch",
        "torchvision", 
        "torchaudio",
        "Pillow",
        "numpy",
        "opencv-python"
    ]
    
    # AI packages
    ai_packages = [
        "realesrgan",
        "rembg"
    ]
    
    # Utility packages
    utility_packages = [
        "tqdm",
        "requests"
    ]
    
    all_packages = essential_packages + ai_packages + utility_packages
    
    print(f"Installing {len(all_packages)} packages...")
    print()
    
    success_count = 0
    failed_packages = []
    
    for package in all_packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
        print()
    
    print("=" * 40)
    print(f"Installation Summary:")
    print(f"✓ Successfully installed: {success_count}/{len(all_packages)}")
    
    if failed_packages:
        print(f"✗ Failed packages: {', '.join(failed_packages)}")
        print("\nYou can try installing failed packages manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
    else:
        print("🎉 All packages installed successfully!")
        print("\nYou can now run the application:")
        print("  python main_gui.py")
    
    print("\nPress Enter to continue...")
    input()

if __name__ == "__main__":
    main()
