#!/usr/bin/env python3
"""
Image Enhancement and Background Removal Application
Simple launcher script
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'torch',
        'realesrgan', 
        'rembg',
        'PIL',
        'cv2',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'cv2':
                import cv2
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """Install missing dependencies"""
    try:
        print("Installing dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        return False

def main():
    """Main function"""
    print("=== محسن الصور ومزيل الخلفية ===")
    print("Image Enhancement and Background Removal Application")
    print("=" * 50)

    # Check if we have a display (for GUI)
    if not os.environ.get('DISPLAY'):
        print("Warning: No display detected. This application requires a GUI environment.")
        print("If you're using SSH, try: ssh -X username@hostname")
        print("Or run the application on a machine with a desktop environment.")
        return

    # Check dependencies
    missing = check_dependencies()

    if missing:
        print(f"Missing packages: {', '.join(missing)}")

        try:
            # Ask user if they want to install
            root = tk.Tk()
            root.withdraw()  # Hide main window

            install = messagebox.askyesno(
                "Missing Dependencies",
                f"The following packages are missing:\n{', '.join(missing)}\n\n"
                "Would you like to install them automatically?"
            )

            root.destroy()

            if install:
                if not install_dependencies():
                    print("Failed to install dependencies. Please install manually:")
                    print("pip install -r requirements.txt")
                    return
            else:
                print("Please install the required dependencies manually:")
                print("pip install -r requirements.txt")
                return
        except Exception as e:
            print(f"GUI error: {e}")
            print("Please install the required dependencies manually:")
            print("pip install -r requirements.txt")
            return

    # Import and run the main application
    try:
        from main_gui import main as run_gui
        print("Starting application...")
        run_gui()
    except Exception as e:
        print(f"Error starting application: {e}")

        try:
            # Show error in GUI
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")
            root.destroy()
        except:
            print("Could not show GUI error dialog.")

if __name__ == "__main__":
    main()
