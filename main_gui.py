"""
Main GUI Application for Image Enhancement and Background Removal
Features tabbed interface with separate sections for each functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from PIL import Image, ImageTk
import logging

# Import our custom modules
from image_enhancer import ImageEnhancer
from background_remover import BackgroundRemover

class ImageProcessorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("محسن الصور ومزيل الخلفية - Image Enhancer & Background Remover")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Initialize processors
        self.image_enhancer = None
        self.background_remover = None
        
        # Setup logging
        self.setup_logging()
        
        # Create GUI
        self.create_widgets()
        
        # Initialize processors in background
        self.initialize_processors()
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def create_widgets(self):
        """Create the main GUI widgets"""
        # Main title
        title_label = tk.Label(
            self.root, 
            text="محسن الصور ومزيل الخلفية",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack(pady=10)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create tabs
        self.create_enhancement_tab()
        self.create_background_removal_tab()
        self.create_log_tab()
    
    def create_enhancement_tab(self):
        """Create the image enhancement tab"""
        # Enhancement frame
        self.enhancement_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.enhancement_frame, text="تحسين الصور")
        
        # Input folder selection
        input_frame = ttk.LabelFrame(self.enhancement_frame, text="اختيار مجلد الصور", padding=10)
        input_frame.pack(fill='x', padx=10, pady=5)
        
        self.enhance_input_path = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.enhance_input_path, width=50).pack(side='left', padx=5)
        ttk.Button(input_frame, text="اختر المجلد", 
                  command=self.select_enhancement_folder).pack(side='right', padx=5)
        
        # Settings frame
        settings_frame = ttk.LabelFrame(self.enhancement_frame, text="إعدادات التحسين", padding=10)
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # Scale selection
        scale_frame = ttk.Frame(settings_frame)
        scale_frame.pack(fill='x', pady=2)
        ttk.Label(scale_frame, text="معامل التكبير:").pack(side='left')
        self.scale_var = tk.StringVar(value="4")
        scale_combo = ttk.Combobox(scale_frame, textvariable=self.scale_var, 
                                  values=["2", "4"], state="readonly", width=10)
        scale_combo.pack(side='left', padx=10)
        
        # GPU usage
        self.use_gpu_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="استخدام كرت الشاشة (GPU)", 
                       variable=self.use_gpu_var).pack(anchor='w', pady=2)
        
        # Device info
        self.device_info_label = ttk.Label(settings_frame, text="جاري التحقق من الجهاز...")
        self.device_info_label.pack(anchor='w', pady=2)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self.enhancement_frame, text="التقدم", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.enhance_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.enhance_progress.pack(fill='x', pady=2)
        
        self.enhance_status_label = ttk.Label(progress_frame, text="جاهز للبدء")
        self.enhance_status_label.pack(anchor='w', pady=2)
        
        # Control buttons
        button_frame = ttk.Frame(self.enhancement_frame)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        self.enhance_button = ttk.Button(button_frame, text="بدء تحسين الصور", 
                                        command=self.start_enhancement)
        self.enhance_button.pack(side='left', padx=5)
        
        ttk.Button(button_frame, text="فتح مجلد النتائج", 
                  command=lambda: self.open_folder('output_enhanced')).pack(side='left', padx=5)
    
    def create_background_removal_tab(self):
        """Create the background removal tab"""
        # Background removal frame
        self.bg_removal_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bg_removal_frame, text="إزالة الخلفية")
        
        # Input folder selection
        input_frame = ttk.LabelFrame(self.bg_removal_frame, text="اختيار مجلد الصور", padding=10)
        input_frame.pack(fill='x', padx=10, pady=5)
        
        self.bg_input_path = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.bg_input_path, width=50).pack(side='left', padx=5)
        ttk.Button(input_frame, text="اختر المجلد", 
                  command=self.select_background_folder).pack(side='right', padx=5)
        
        # Settings frame
        settings_frame = ttk.LabelFrame(self.bg_removal_frame, text="إعدادات الإزالة", padding=10)
        settings_frame.pack(fill='x', padx=10, pady=5)
        
        # Model selection
        model_frame = ttk.Frame(settings_frame)
        model_frame.pack(fill='x', pady=2)
        ttk.Label(model_frame, text="نموذج الإزالة:").pack(side='left')
        self.bg_model_var = tk.StringVar(value="u2net")
        model_combo = ttk.Combobox(model_frame, textvariable=self.bg_model_var, 
                                  values=["u2net", "u2netp", "u2net_human_seg", "silueta"], 
                                  state="readonly", width=15)
        model_combo.pack(side='left', padx=10)
        
        # Output format
        self.save_transparent_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(settings_frame, text="حفظ بخلفية شفافة (PNG)", 
                       variable=self.save_transparent_var).pack(anchor='w', pady=2)
        
        # Progress frame
        progress_frame = ttk.LabelFrame(self.bg_removal_frame, text="التقدم", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)
        
        self.bg_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.bg_progress.pack(fill='x', pady=2)
        
        self.bg_status_label = ttk.Label(progress_frame, text="جاهز للبدء")
        self.bg_status_label.pack(anchor='w', pady=2)
        
        # Control buttons
        button_frame = ttk.Frame(self.bg_removal_frame)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        self.bg_button = ttk.Button(button_frame, text="بدء إزالة الخلفية", 
                                   command=self.start_background_removal)
        self.bg_button.pack(side='left', padx=5)
        
        ttk.Button(button_frame, text="فتح مجلد النتائج", 
                  command=lambda: self.open_folder('output_background_removed')).pack(side='left', padx=5)
    
    def create_log_tab(self):
        """Create the log tab"""
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="السجل")
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(self.log_frame, height=20, width=80)
        self.log_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Clear log button
        ttk.Button(self.log_frame, text="مسح السجل", 
                  command=self.clear_log).pack(pady=5)
    
    def initialize_processors(self):
        """Initialize the image processors in background"""
        def init_thread():
            try:
                self.log_message("جاري تهيئة معالجات الصور...")
                
                # Initialize image enhancer
                self.image_enhancer = ImageEnhancer(use_gpu=self.use_gpu_var.get())
                device_info = self.image_enhancer.get_device_info()
                self.device_info_label.config(text=f"الجهاز المستخدم: {device_info}")
                
                # Initialize background remover
                self.background_remover = BackgroundRemover()
                
                self.log_message("تم تهيئة جميع المعالجات بنجاح!")
                
            except Exception as e:
                self.log_message(f"خطأ في التهيئة: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في تهيئة المعالجات: {str(e)}")
        
        threading.Thread(target=init_thread, daemon=True).start()
    
    def select_enhancement_folder(self):
        """Select folder for image enhancement"""
        folder = filedialog.askdirectory(title="اختر مجلد الصور للتحسين")
        if folder:
            self.enhance_input_path.set(folder)
    
    def select_background_folder(self):
        """Select folder for background removal"""
        folder = filedialog.askdirectory(title="اختر مجلد الصور لإزالة الخلفية")
        if folder:
            self.bg_input_path.set(folder)

    def start_enhancement(self):
        """Start image enhancement process"""
        if not self.image_enhancer:
            messagebox.showerror("خطأ", "لم يتم تهيئة معالج تحسين الصور بعد")
            return

        input_folder = self.enhance_input_path.get()
        if not input_folder or not os.path.exists(input_folder):
            messagebox.showerror("خطأ", "يرجى اختيار مجلد صحيح")
            return

        def enhancement_thread():
            try:
                self.enhance_button.config(state='disabled')
                self.enhance_progress['value'] = 0

                # Update enhancer settings
                scale = int(self.scale_var.get())
                use_gpu = self.use_gpu_var.get()

                # Reinitialize if settings changed
                if (self.image_enhancer.scale != scale or
                    self.image_enhancer.use_gpu != use_gpu):
                    self.log_message("إعادة تهيئة المعالج بالإعدادات الجديدة...")
                    self.image_enhancer = ImageEnhancer(scale=scale, use_gpu=use_gpu)

                # Progress callback
                def progress_callback(progress, message):
                    self.enhance_progress['value'] = progress
                    self.enhance_status_label.config(text=message)
                    self.log_message(f"تحسين الصور: {message}")

                # Start enhancement
                self.log_message(f"بدء تحسين الصور من: {input_folder}")
                enhanced_paths = self.image_enhancer.enhance_folder(
                    input_folder,
                    progress_callback=progress_callback
                )

                self.enhance_progress['value'] = 100
                self.enhance_status_label.config(text=f"تم الانتهاء! تم تحسين {len(enhanced_paths)} صورة")
                self.log_message(f"تم الانتهاء من تحسين {len(enhanced_paths)} صورة")

                messagebox.showinfo("نجح", f"تم تحسين {len(enhanced_paths)} صورة بنجاح!")

            except Exception as e:
                self.log_message(f"خطأ في تحسين الصور: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في تحسين الصور: {str(e)}")
            finally:
                self.enhance_button.config(state='normal')

        threading.Thread(target=enhancement_thread, daemon=True).start()

    def start_background_removal(self):
        """Start background removal process"""
        if not self.background_remover:
            messagebox.showerror("خطأ", "لم يتم تهيئة معالج إزالة الخلفية بعد")
            return

        input_folder = self.bg_input_path.get()
        if not input_folder or not os.path.exists(input_folder):
            messagebox.showerror("خطأ", "يرجى اختيار مجلد صحيح")
            return

        def removal_thread():
            try:
                self.bg_button.config(state='disabled')
                self.bg_progress['value'] = 0

                # Update model if changed
                current_model = self.bg_model_var.get()
                if self.background_remover.model_name != current_model:
                    self.log_message(f"تغيير النموذج إلى: {current_model}")
                    self.background_remover.change_model(current_model)

                # Progress callback
                def progress_callback(progress, message):
                    self.bg_progress['value'] = progress
                    self.bg_status_label.config(text=message)
                    self.log_message(f"إزالة الخلفية: {message}")

                # Start background removal
                self.log_message(f"بدء إزالة الخلفية من: {input_folder}")
                processed_paths = self.background_remover.remove_background_folder(
                    input_folder,
                    return_rgba=self.save_transparent_var.get(),
                    progress_callback=progress_callback
                )

                self.bg_progress['value'] = 100
                self.bg_status_label.config(text=f"تم الانتهاء! تم معالجة {len(processed_paths)} صورة")
                self.log_message(f"تم الانتهاء من معالجة {len(processed_paths)} صورة")

                messagebox.showinfo("نجح", f"تم معالجة {len(processed_paths)} صورة بنجاح!")

            except Exception as e:
                self.log_message(f"خطأ في إزالة الخلفية: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في إزالة الخلفية: {str(e)}")
            finally:
                self.bg_button.config(state='normal')

        threading.Thread(target=removal_thread, daemon=True).start()

    def open_folder(self, folder_name):
        """Open output folder in file explorer"""
        if os.path.exists(folder_name):
            if sys.platform == "win32":
                os.startfile(folder_name)
            elif sys.platform == "darwin":
                os.system(f"open {folder_name}")
            else:
                os.system(f"xdg-open {folder_name}")
        else:
            messagebox.showwarning("تحذير", f"المجلد {folder_name} غير موجود")

    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)

    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ImageProcessorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
