#!/usr/bin/env python3
"""
Command line image processing script
Process images without GUI interface
"""

import os
import sys
import argparse
from pathlib import Path

def enhance_images(input_folder, output_folder=None, scale=4):
    """Enhance images in a folder"""
    print(f"🚀 Starting image enhancement...")
    print(f"Input folder: {input_folder}")
    
    try:
        from image_enhancer import ImageEnhancer
        
        # Initialize enhancer
        enhancer = ImageEnhancer(scale=scale, use_gpu=False)
        print(f"Device: {enhancer.get_device_info()}")
        
        # Process folder
        def progress_callback(progress, message):
            print(f"Progress: {progress:.1f}% - {message}")
        
        enhanced_paths = enhancer.enhance_folder(
            input_folder, 
            output_folder, 
            progress_callback=progress_callback
        )
        
        print(f"✅ Enhanced {len(enhanced_paths)} images successfully!")
        print(f"Output folder: {output_folder or 'output_enhanced'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error enhancing images: {e}")
        return False

def remove_backgrounds(input_folder, output_folder=None, model='u2net', transparent=True):
    """Remove backgrounds from images in a folder"""
    print(f"🎨 Starting background removal...")
    print(f"Input folder: {input_folder}")
    print(f"Model: {model}")
    
    try:
        from background_remover import BackgroundRemover
        
        # Initialize background remover
        bg_remover = BackgroundRemover(model_name=model)
        print(f"Available models: {bg_remover.get_available_models()}")
        
        # Process folder
        def progress_callback(progress, message):
            print(f"Progress: {progress:.1f}% - {message}")
        
        processed_paths = bg_remover.remove_background_folder(
            input_folder,
            output_folder,
            return_rgba=transparent,
            progress_callback=progress_callback
        )
        
        print(f"✅ Processed {len(processed_paths)} images successfully!")
        print(f"Output folder: {output_folder or 'output_background_removed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error removing backgrounds: {e}")
        return False

def list_supported_formats():
    """List supported image formats"""
    formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    print("Supported image formats:")
    for fmt in formats:
        print(f"  - {fmt}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Image Enhancement and Background Removal CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Enhance images
  python process_images.py enhance input_folder

  # Remove backgrounds
  python process_images.py remove-bg input_folder

  # Enhance with custom scale
  python process_images.py enhance input_folder --scale 2

  # Remove backgrounds with specific model
  python process_images.py remove-bg input_folder --model u2net_human_seg

  # List supported formats
  python process_images.py formats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Enhance command
    enhance_parser = subparsers.add_parser('enhance', help='Enhance images')
    enhance_parser.add_argument('input_folder', help='Input folder containing images')
    enhance_parser.add_argument('--output', '-o', help='Output folder (default: output_enhanced)')
    enhance_parser.add_argument('--scale', '-s', type=int, choices=[2, 4], default=4,
                               help='Upscaling factor (default: 4)')
    
    # Remove background command
    bg_parser = subparsers.add_parser('remove-bg', help='Remove backgrounds')
    bg_parser.add_argument('input_folder', help='Input folder containing images')
    bg_parser.add_argument('--output', '-o', help='Output folder (default: output_background_removed)')
    bg_parser.add_argument('--model', '-m', default='u2net',
                          choices=['u2net', 'u2netp', 'u2net_human_seg', 'silueta'],
                          help='Background removal model (default: u2net)')
    bg_parser.add_argument('--no-transparent', action='store_true',
                          help='Save with white background instead of transparent')
    
    # Formats command
    subparsers.add_parser('formats', help='List supported image formats')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    print("=== محسن الصور ومزيل الخلفية (CLI Mode) ===")
    print("Image Enhancement & Background Removal CLI")
    print("=" * 50)
    
    if args.command == 'formats':
        list_supported_formats()
        return 0
    
    # Check input folder
    if not os.path.exists(args.input_folder):
        print(f"❌ Error: Input folder '{args.input_folder}' does not exist")
        return 1
    
    # Count images in input folder
    input_path = Path(args.input_folder)
    supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    image_files = [f for f in input_path.iterdir() 
                   if f.is_file() and f.suffix.lower() in supported_extensions]
    
    if not image_files:
        print(f"❌ No supported images found in '{args.input_folder}'")
        list_supported_formats()
        return 1
    
    print(f"📁 Found {len(image_files)} images to process")
    
    # Process based on command
    if args.command == 'enhance':
        success = enhance_images(args.input_folder, args.output, args.scale)
    elif args.command == 'remove-bg':
        success = remove_backgrounds(
            args.input_folder, 
            args.output, 
            args.model, 
            not args.no_transparent
        )
    else:
        print(f"❌ Unknown command: {args.command}")
        return 1
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
