#!/usr/bin/env python3
"""
Command Line Interface for testing the image processing modules
"""

import os
import sys
from pathlib import Path

def test_image_enhancement():
    """Test image enhancement functionality"""
    print("Testing Image Enhancement...")
    
    try:
        from image_enhancer import ImageEnhancer
        
        # Initialize enhancer
        enhancer = ImageEnhancer(scale=2, use_gpu=False)
        print(f"Device info: {enhancer.get_device_info()}")
        
        # Create test directories
        os.makedirs('input', exist_ok=True)
        os.makedirs('output_enhanced', exist_ok=True)
        
        print("✓ Image enhancer initialized successfully")
        print("Note: To test enhancement, place images in the 'input' folder")
        
        return True
        
    except Exception as e:
        print(f"✗ Image enhancement test failed: {e}")
        return False

def test_background_removal():
    """Test background removal functionality"""
    print("\nTesting Background Removal...")
    
    try:
        from background_remover import BackgroundRemover
        
        # Initialize background remover
        bg_remover = BackgroundRemover()
        print(f"Available models: {bg_remover.get_available_models()}")
        
        # Create test directories
        os.makedirs('input', exist_ok=True)
        os.makedirs('output_background_removed', exist_ok=True)
        
        print("✓ Background remover initialized successfully")
        print("Note: To test background removal, place images in the 'input' folder")
        
        return True
        
    except Exception as e:
        print(f"✗ Background removal test failed: {e}")
        return False

def process_sample_images():
    """Process any images found in the input folder"""
    input_folder = Path('input')
    
    if not input_folder.exists() or not any(input_folder.iterdir()):
        print("\nNo images found in 'input' folder for testing.")
        print("Place some images in the 'input' folder to test processing.")
        return
    
    print(f"\nFound images in input folder. Processing...")
    
    # Test image enhancement
    try:
        from image_enhancer import ImageEnhancer
        enhancer = ImageEnhancer(scale=2, use_gpu=False)
        
        image_files = [f for f in input_folder.iterdir() 
                      if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
        
        if image_files:
            print(f"Enhancing {len(image_files)} images...")
            for img_file in image_files[:2]:  # Process only first 2 images for testing
                try:
                    output_path = enhancer.enhance_image(str(img_file))
                    print(f"✓ Enhanced: {img_file.name} -> {output_path}")
                except Exception as e:
                    print(f"✗ Failed to enhance {img_file.name}: {e}")
    
    except Exception as e:
        print(f"Enhancement test failed: {e}")
    
    # Test background removal
    try:
        from background_remover import BackgroundRemover
        bg_remover = BackgroundRemover()
        
        if image_files:
            print(f"Removing background from {len(image_files)} images...")
            for img_file in image_files[:2]:  # Process only first 2 images for testing
                try:
                    output_path = bg_remover.remove_background(str(img_file))
                    print(f"✓ Background removed: {img_file.name} -> {output_path}")
                except Exception as e:
                    print(f"✗ Failed to remove background from {img_file.name}: {e}")
    
    except Exception as e:
        print(f"Background removal test failed: {e}")

def main():
    """Main function"""
    print("=== Image Processing CLI Test ===")
    print("Testing image processing modules without GUI")
    print("=" * 40)
    
    # Test modules
    enhancement_ok = test_image_enhancement()
    bg_removal_ok = test_background_removal()
    
    if enhancement_ok and bg_removal_ok:
        print("\n✓ All modules loaded successfully!")
        
        # Process sample images if available
        process_sample_images()
        
        print("\n" + "=" * 40)
        print("Test completed successfully!")
        print("The application modules are working correctly.")
        print("To use the GUI version, ensure you have a display environment.")
        
    else:
        print("\n✗ Some modules failed to load.")
        print("Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
