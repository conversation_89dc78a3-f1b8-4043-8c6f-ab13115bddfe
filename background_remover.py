"""
Background Removal <PERSON>le using rembg
Professional background removal for images
"""

import os
import logging
from PIL import Image
import numpy as np
from rembg import remove, new_session
from io import BytesIO

class BackgroundRemover:
    def __init__(self, model_name='u2net'):
        """
        Initialize the Background Remover
        
        Args:
            model_name (str): Model to use for background removal
                            Options: 'u2net', 'u2netp', 'u2net_human_seg', 'silueta'
        """
        self.model_name = model_name
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Initialize the model session
        self.session = None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the rembg model session"""
        try:
            self.session = new_session(self.model_name)
            self.logger.info(f"Background removal model '{self.model_name}' initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize model: {str(e)}")
            # Fallback to default model
            try:
                self.session = new_session('u2net')
                self.model_name = 'u2net'
                self.logger.info("Fallback to default u2net model")
            except Exception as fallback_error:
                self.logger.error(f"Failed to initialize fallback model: {str(fallback_error)}")
                raise
    
    def remove_background(self, input_path, output_path=None, return_rgba=True):
        """
        Remove background from a single image
        
        Args:
            input_path (str): Path to input image
            output_path (str): Path to save processed image
            return_rgba (bool): Whether to return RGBA image (with transparency)
            
        Returns:
            str: Path to processed image
        """
        try:
            # Read input image
            with open(input_path, 'rb') as input_file:
                input_data = input_file.read()
            
            self.logger.info(f"Removing background from: {input_path}")
            
            # Remove background
            output_data = remove(input_data, session=self.session)
            
            # Convert to PIL Image
            output_image = Image.open(BytesIO(output_data))
            
            # Generate output path if not provided
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = os.path.join('output_background_removed', f"{base_name}_no_bg.png")
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save the image
            if return_rgba:
                # Save as PNG with transparency
                output_image.save(output_path, 'PNG')
            else:
                # Convert to RGB and save as JPEG
                if output_image.mode == 'RGBA':
                    # Create white background
                    background = Image.new('RGB', output_image.size, (255, 255, 255))
                    background.paste(output_image, mask=output_image.split()[-1])
                    output_image = background
                
                output_path = output_path.replace('.png', '.jpg')
                output_image.save(output_path, 'JPEG', quality=95)
            
            self.logger.info(f"Background removed image saved: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to remove background from {input_path}: {str(e)}")
            raise
    
    def remove_background_folder(self, input_folder, output_folder=None, 
                                return_rgba=True, progress_callback=None):
        """
        Remove background from all images in a folder
        
        Args:
            input_folder (str): Path to input folder
            output_folder (str): Path to output folder
            return_rgba (bool): Whether to return RGBA images
            progress_callback (callable): Callback function for progress updates
            
        Returns:
            list: List of processed image paths
        """
        if output_folder is None:
            output_folder = 'output_background_removed'
        
        # Create output folder
        os.makedirs(output_folder, exist_ok=True)
        
        # Supported image extensions
        supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # Get all image files
        image_files = []
        for file in os.listdir(input_folder):
            if os.path.splitext(file.lower())[1] in supported_extensions:
                image_files.append(file)
        
        if not image_files:
            raise ValueError("No supported image files found in the input folder")
        
        processed_paths = []
        total_files = len(image_files)
        
        for i, filename in enumerate(image_files):
            try:
                input_path = os.path.join(input_folder, filename)
                base_name = os.path.splitext(filename)[0]
                
                if return_rgba:
                    output_path = os.path.join(output_folder, f"{base_name}_no_bg.png")
                else:
                    output_path = os.path.join(output_folder, f"{base_name}_no_bg.jpg")
                
                # Remove background
                processed_path = self.remove_background(input_path, output_path, return_rgba)
                processed_paths.append(processed_path)
                
                # Update progress
                if progress_callback:
                    progress = (i + 1) / total_files * 100
                    progress_callback(progress, f"Processed: {filename}")
                
            except Exception as e:
                self.logger.error(f"Failed to process {filename}: {str(e)}")
                if progress_callback:
                    progress_callback((i + 1) / total_files * 100, f"Error: {filename}")
        
        return processed_paths
    
    def get_available_models(self):
        """Get list of available models"""
        return ['u2net', 'u2netp', 'u2net_human_seg', 'silueta', 'isnet-general-use']
    
    def change_model(self, model_name):
        """
        Change the background removal model
        
        Args:
            model_name (str): New model name
        """
        try:
            self.model_name = model_name
            self._initialize_model()
            self.logger.info(f"Model changed to: {model_name}")
        except Exception as e:
            self.logger.error(f"Failed to change model to {model_name}: {str(e)}")
            raise
    
    def create_preview(self, input_path, max_size=(400, 400)):
        """
        Create a preview of the background removal result
        
        Args:
            input_path (str): Path to input image
            max_size (tuple): Maximum size for preview
            
        Returns:
            PIL.Image: Preview image
        """
        try:
            # Read and process image
            with open(input_path, 'rb') as input_file:
                input_data = input_file.read()
            
            output_data = remove(input_data, session=self.session)
            output_image = Image.open(BytesIO(output_data))
            
            # Resize for preview
            output_image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            return output_image
            
        except Exception as e:
            self.logger.error(f"Failed to create preview for {input_path}: {str(e)}")
            raise
