"""
Image Enhancement Module using Real-ESRGAN
Supports GPU acceleration for faster processing
"""

import os
import cv2
import logging
try:
    import torch
    from realesrgan import RealESRGANer
    from realesrgan.archs.srvgg_arch import SRVGGNetCompact
    REALESRGAN_AVAILABLE = True
except ImportError as e:
    REALESRGAN_AVAILABLE = False
    print(f"Warning: Real-ESRGAN not available: {e}")
    print("Image enhancement will use basic OpenCV upscaling instead.")

class ImageEnhancer:
    def __init__(self, model_name='RealESRGAN_x4plus', scale=4, use_gpu=True):
        """
        Initialize the Image Enhancer

        Args:
            model_name (str): Model to use for enhancement
            scale (int): Upscaling factor (2, 4, 8)
            use_gpu (bool): Whether to use GPU acceleration
        """
        self.model_name = model_name
        self.scale = scale
        self.use_gpu = use_gpu and REALESRGAN_AVAILABLE
        if REALESRGAN_AVAILABLE and torch.cuda.is_available():
            self.use_gpu = self.use_gpu and torch.cuda.is_available()
            self.device = 'cuda' if self.use_gpu else 'cpu'
        else:
            self.use_gpu = False
            self.device = 'cpu'

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Initialize the model
        self.upsampler = None
        if REALESRGAN_AVAILABLE:
            self._initialize_model()
        else:
            self.logger.warning("Real-ESRGAN not available, using basic OpenCV upscaling")
    
    def _initialize_model(self):
        """Initialize the Real-ESRGAN model"""
        if not REALESRGAN_AVAILABLE:
            self.logger.warning("Real-ESRGAN not available")
            return

        try:
            # Model configurations
            if self.model_name == 'RealESRGAN_x4plus':
                model = SRVGGNetCompact(num_in_ch=3, num_out_ch=3, num_feat=64,
                                      num_conv=32, upscale=4, act_type='prelu')
                model_path = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth'
            elif self.model_name == 'RealESRGAN_x2plus':
                model = SRVGGNetCompact(num_in_ch=3, num_out_ch=3, num_feat=64,
                                      num_conv=32, upscale=2, act_type='prelu')
                model_path = 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x2plus.pth'
            else:
                raise ValueError(f"Unsupported model: {self.model_name}")

            # Initialize upsampler
            self.upsampler = RealESRGANer(
                scale=self.scale,
                model_path=model_path,
                model=model,
                tile=0,
                tile_pad=10,
                pre_pad=0,
                half=self.use_gpu,  # Use half precision on GPU
                gpu_id=0 if self.use_gpu else None
            )

            self.logger.info(f"Model initialized successfully on {self.device}")

        except Exception as e:
            self.logger.error(f"Failed to initialize model: {str(e)}")
            self.logger.info("Falling back to basic OpenCV upscaling")
            self.upsampler = None
    
    def enhance_image(self, input_path, output_path=None):
        """
        Enhance a single image

        Args:
            input_path (str): Path to input image
            output_path (str): Path to save enhanced image

        Returns:
            str: Path to enhanced image
        """
        try:
            # Read image
            img = cv2.imread(input_path, cv2.IMREAD_COLOR)
            if img is None:
                raise ValueError(f"Could not read image: {input_path}")

            # Enhance image
            self.logger.info(f"Enhancing image: {input_path}")

            if self.upsampler is not None and REALESRGAN_AVAILABLE:
                # Use Real-ESRGAN
                output, _ = self.upsampler.enhance(img, outscale=self.scale)
            else:
                # Use basic OpenCV upscaling as fallback
                height, width = img.shape[:2]
                new_width = int(width * self.scale)
                new_height = int(height * self.scale)
                output = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                self.logger.info("Using basic OpenCV upscaling (Real-ESRGAN not available)")

            # Generate output path if not provided
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = os.path.join('output_enhanced', f"{base_name}_enhanced.png")

            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Save enhanced image
            cv2.imwrite(output_path, output)
            self.logger.info(f"Enhanced image saved: {output_path}")

            return output_path

        except Exception as e:
            self.logger.error(f"Failed to enhance image {input_path}: {str(e)}")
            raise
    
    def enhance_folder(self, input_folder, output_folder=None, progress_callback=None):
        """
        Enhance all images in a folder
        
        Args:
            input_folder (str): Path to input folder
            output_folder (str): Path to output folder
            progress_callback (callable): Callback function for progress updates
            
        Returns:
            list: List of enhanced image paths
        """
        if output_folder is None:
            output_folder = 'output_enhanced'
        
        # Create output folder
        os.makedirs(output_folder, exist_ok=True)
        
        # Supported image extensions
        supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # Get all image files
        image_files = []
        for file in os.listdir(input_folder):
            if os.path.splitext(file.lower())[1] in supported_extensions:
                image_files.append(file)
        
        if not image_files:
            raise ValueError("No supported image files found in the input folder")
        
        enhanced_paths = []
        total_files = len(image_files)
        
        for i, filename in enumerate(image_files):
            try:
                input_path = os.path.join(input_folder, filename)
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_folder, f"{base_name}_enhanced.png")
                
                # Enhance image
                enhanced_path = self.enhance_image(input_path, output_path)
                enhanced_paths.append(enhanced_path)
                
                # Update progress
                if progress_callback:
                    progress = (i + 1) / total_files * 100
                    progress_callback(progress, f"Enhanced: {filename}")
                
            except Exception as e:
                self.logger.error(f"Failed to enhance {filename}: {str(e)}")
                if progress_callback:
                    progress_callback((i + 1) / total_files * 100, f"Error: {filename}")
        
        return enhanced_paths
    
    def get_device_info(self):
        """Get information about the device being used"""
        if not REALESRGAN_AVAILABLE:
            return "CPU (OpenCV fallback - Real-ESRGAN not available)"
        elif self.use_gpu and torch.cuda.is_available():
            return f"GPU: {torch.cuda.get_device_name(0)}"
        else:
            return "CPU"
