#!/usr/bin/env python3
"""
Run the GUI application with virtual display (for headless servers)
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

def start_virtual_display():
    """Start Xvfb virtual display"""
    print("Starting virtual display...")
    
    # Kill any existing Xvfb processes
    try:
        subprocess.run(['pkill', 'Xvfb'], check=False)
        time.sleep(1)
    except:
        pass
    
    # Start Xvfb
    xvfb_process = subprocess.Popen([
        'Xvfb', ':99', 
        '-screen', '0', '1024x768x24',
        '-ac', '+extension', 'GLX'
    ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    # Set DISPLAY environment variable
    os.environ['DISPLAY'] = ':99'
    
    # Wait a moment for Xvfb to start
    time.sleep(2)
    
    print("✓ Virtual display started on :99")
    return xvfb_process

def run_gui_application():
    """Run the main GUI application"""
    print("Starting GUI application...")
    
    try:
        # Import and run the main application
        from main_gui import main as run_gui
        run_gui()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Error running GUI application: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=== محسن الصور ومزيل الخلفية (Virtual Display Mode) ===")
    print("Image Enhancement & Background Removal with Virtual Display")
    print("=" * 60)
    
    # Check if we're in a virtual environment
    if not os.environ.get('VIRTUAL_ENV'):
        print("Warning: Not running in a virtual environment")
        print("Make sure you have activated the virtual environment:")
        print("source venv/bin/activate")
        print()
    
    xvfb_process = None
    
    try:
        # Start virtual display
        xvfb_process = start_virtual_display()
        
        # Test if display is working
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()
            root.destroy()
            print("✓ Virtual display is working")
        except Exception as e:
            print(f"✗ Virtual display test failed: {e}")
            return 1
        
        # Run the GUI application
        success = run_gui_application()
        
        if success:
            print("Application completed successfully")
        else:
            print("Application encountered errors")
            return 1
            
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        # Clean up virtual display
        if xvfb_process:
            print("Stopping virtual display...")
            xvfb_process.terminate()
            try:
                xvfb_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                xvfb_process.kill()
            print("✓ Virtual display stopped")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
