#!/usr/bin/env python3
"""
Simple test for the GUI without heavy dependencies
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

def test_gui():
    """Test the basic GUI structure"""
    root = tk.Tk()
    root.title("محسن الصور ومزيل الخلفية - اختبار")
    root.geometry("600x400")
    
    # Main title
    title_label = tk.Label(
        root, 
        text="محسن الصور ومزيل الخلفية - اختبار",
        font=("Arial", 14, "bold")
    )
    title_label.pack(pady=10)
    
    # Create notebook for tabs
    notebook = ttk.Notebook(root)
    notebook.pack(fill='both', expand=True, padx=10, pady=5)
    
    # Enhancement tab
    enhancement_frame = ttk.Frame(notebook)
    notebook.add(enhancement_frame, text="تحسين الصور")
    
    ttk.Label(enhancement_frame, text="تبويب تحسين الصور", 
              font=("Arial", 12)).pack(pady=20)
    ttk.Button(enhancement_frame, text="اختبار", 
               command=lambda: messagebox.showinfo("اختبار", "تبويب تحسين الصور يعمل!")).pack(pady=10)
    
    # Background removal tab
    bg_frame = ttk.Frame(notebook)
    notebook.add(bg_frame, text="إزالة الخلفية")
    
    ttk.Label(bg_frame, text="تبويب إزالة الخلفية", 
              font=("Arial", 12)).pack(pady=20)
    ttk.Button(bg_frame, text="اختبار", 
               command=lambda: messagebox.showinfo("اختبار", "تبويب إزالة الخلفية يعمل!")).pack(pady=10)
    
    # Status
    status_frame = ttk.Frame(root)
    status_frame.pack(fill='x', padx=10, pady=5)
    
    ttk.Label(status_frame, text="الحالة: جاهز للاختبار").pack(side='left')
    
    # Check folders
    folders_exist = all(os.path.exists(folder) for folder in 
                       ['input', 'output_enhanced', 'output_background_removed'])
    
    if folders_exist:
        ttk.Label(status_frame, text="✓ المجلدات موجودة", 
                 foreground='green').pack(side='right')
    else:
        ttk.Label(status_frame, text="✗ المجلدات مفقودة", 
                 foreground='red').pack(side='right')
    
    root.mainloop()

if __name__ == "__main__":
    print("اختبار الواجهة الرسومية...")
    test_gui()
